#!/usr/bin/env python3
"""
Intent Structuring Agent for chatBIS.

This agent translates natural language queries into structured ActionRequest JSON format
that can be executed by the ExecutionAgent. It handles incomplete information by
requesting clarification from the user.
"""

import json
import logging
from typing import Dict, List, Optional, Union
from datetime import datetime

from langchain_core.messages import SystemMessage, HumanMessage
from pydantic import ValidationError

from ..models import ActionRequest, ClarificationRequest, ActionType, EntityType

logger = logging.getLogger(__name__)


class IntentParserAgent:
    """Agent that parses natural language into structured ActionRequest format."""
    
    def __init__(self, llm):
        """
        Initialize the Intent Parser Agent.
        
        Args:
            llm: Language model instance for generating structured responses
        """
        self.llm = llm
        
    def parse_intent(self, user_query: str, session_id: Optional[str] = None, 
                    conversation_history: Optional[List] = None) -> Union[ActionRequest, ClarificationRequest]:
        """
        Parse user query into structured ActionRequest or request clarification.
        
        Args:
            user_query: Natural language query from user
            session_id: Session identifier for context
            conversation_history: Previous conversation messages for context
            
        Returns:
            Either a validated ActionRequest or a ClarificationRequest
        """
        try:
            # Build the system prompt with examples and schema
            system_prompt = self._build_system_prompt()
            
            # Build the user message with context
            user_message = self._build_user_message(user_query, conversation_history)
            
            # Get LLM response
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_message)
            ]
            
            response = self.llm.invoke(messages)
            response_content = response.content.strip()

            # Clean response to remove <think></think> tags
            cleaned_content = self._clean_response(response_content)

            logger.info(f"LLM response for intent parsing: {response_content[:200]}...")
            logger.info(f"Cleaned response: {cleaned_content[:200]}...")

            # Try to parse as JSON
            try:
                parsed_json = json.loads(cleaned_content)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                return ClarificationRequest(
                    message="I had trouble understanding your request. Could you please rephrase it more clearly?"
                )
            
            # Check if it's a clarification request
            if parsed_json.get("status") == "clarification_needed":
                return ClarificationRequest(**parsed_json)
            
            # Try to create ActionRequest
            try:
                # Add metadata
                parsed_json["user_query"] = user_query
                parsed_json["session_id"] = session_id
                parsed_json["timestamp"] = datetime.now().isoformat()
                
                action_request = ActionRequest(**parsed_json)
                logger.info(f"Successfully parsed intent into {len(action_request.actions)} actions")
                return action_request
                
            except ValidationError as e:
                logger.error(f"Validation error creating ActionRequest: {e}")
                return ClarificationRequest(
                    message=f"I need more information to complete your request. {str(e)}"
                )
                
        except Exception as e:
            logger.error(f"Error in intent parsing: {e}")
            return ClarificationRequest(
                message="I encountered an error processing your request. Please try again."
            )
    
    def _build_system_prompt(self) -> str:
        """Build the system prompt with examples and schema."""
        return """You are an expert openBIS API user who translates natural language queries into structured JSON format for execution with pybis.

CRITICAL: You must ONLY output valid JSON. Do not include any explanations, markdown, or thinking process.

FORBIDDEN:
- Do NOT use <think> tags
- Do NOT add explanations before or after the JSON
- Do NOT use markdown formatting
- Do NOT include any text other than the JSON object

REQUIRED FORMAT:
- Start your response with {
- End your response with }
- Output only valid JSON that can be parsed directly

Your task is to convert user queries into a specific JSON structure that represents actions to be performed on openBIS entities.

RULES:
1. Map natural language to the correct action types: GET (single entity), LIST (multiple entities), CREATE (new entity), UPDATE (modify entity)
2. For "last N" or "recent" queries, use LIST action with count parameter
3. If information is missing, output a clarification request instead

JSON SCHEMA:
{
  "actions": [
    {
      "action": "GET" | "LIST" | "CREATE" | "UPDATE",
      "entity": "SAMPLE" | "EXPERIMENT" | "DATASET" | "PROJECT" | "SPACE",
      "lookup_criteria": {
        "identifier": "/SPACE/PROJECT/OBJECT",  // For GET/UPDATE
        "space": "SPACE_CODE",                  // For LIST
        "type": "ENTITY_TYPE",                  // For LIST/CREATE
        "properties": {"PROP_NAME": "value"}    // For LIST filtering
      },
      "payload": {
        "code": "OBJECT_CODE",                  // For CREATE/UPDATE
        "type": "ENTITY_TYPE",                  // For CREATE
        "space": "SPACE_CODE",                  // For CREATE
        "properties": {"PROP_NAME": "value"}    // For CREATE/UPDATE
      }
    }
  ]
}

EXAMPLES:

Query: "Show me sample ABC123"
Response: {"actions": [{"action": "GET", "entity": "SAMPLE", "lookup_criteria": {"identifier": "ABC123"}}]}

Query: "List all samples in MY_SPACE"
Response: {"actions": [{"action": "LIST", "entity": "SAMPLE", "lookup_criteria": {"space": "MY_SPACE"}}]}

Query: "Give me my last 20 samples"
Response: {"actions": [{"action": "LIST", "entity": "SAMPLE", "lookup_criteria": {"count": 20}}]}

Query: "Show me all my projects"
Response: {"actions": [{"action": "LIST", "entity": "PROJECT", "lookup_criteria": {}}]}

Query: "Give me all of my objects"
Response: {"actions": [{"action": "LIST", "entity": "SAMPLE", "lookup_criteria": {}}]}

Query: "Create a new sample called TEST01 of type YEAST in MY_SPACE"
Response: {"actions": [{"action": "CREATE", "entity": "SAMPLE", "payload": {"code": "TEST01", "type": "YEAST", "space": "MY_SPACE"}}]}

Query: "Find samples with property NAME containing test"
Response: {"actions": [{"action": "LIST", "entity": "SAMPLE", "lookup_criteria": {"properties": {"$NAME": "*test*"}}}]}

CLARIFICATION FORMAT (when information is missing):
{"status": "clarification_needed", "message": "What type should the new sample have?", "missing_fields": ["type"]}

ENTITY TYPE MAPPING:
- samples, sample → SAMPLE
- experiments, experiment → EXPERIMENT  
- datasets, dataset → DATASET
- projects, project → PROJECT
- spaces, space → SPACE

ACTION TYPE MAPPING:
- "show me", "get", "find specific" → GET
- "list", "show all", "find all", "give me all", "my last N", "recent" → LIST
- "create", "make", "add new" → CREATE
- "update", "modify", "change" → UPDATE

COMMON QUERY PATTERNS:
- "my last N [entity]" → LIST with count=N
- "all my [entity]" → LIST with empty lookup_criteria
- "give me all of my objects" → LIST SAMPLE (default to samples)
- "[entity] in [space]" → LIST with space filter
- "recent [entity]" → LIST with count=10 (default recent count)

IMPORTANT:
- Always default to SAMPLE entity when user says "objects" or is unclear
- For "last N" queries, use count parameter in lookup_criteria
- For "all" queries without space, use empty lookup_criteria {}

FINAL REMINDER:
Your response must be ONLY a JSON object. Start with { and end with }.
Do not include <think> tags, explanations, or any other text.
Example response format: {"actions": [{"action": "LIST", "entity": "SAMPLE", "lookup_criteria": {"count": 20}}]}"""

    def _build_user_message(self, user_query: str, conversation_history: Optional[List] = None) -> str:
        """Build the user message with context."""
        message = f"Query: {user_query}"
        
        if conversation_history:
            # Add relevant context from conversation history
            context_lines = []
            for msg in conversation_history[-3:]:  # Last 3 messages for context
                if hasattr(msg, 'content'):
                    content = msg.content[:100]  # Truncate for brevity
                    context_lines.append(f"- {content}")
            
            if context_lines:
                message = f"Recent context:\n" + "\n".join(context_lines) + f"\n\nCurrent query: {user_query}"
        
        return message

    def _clean_response(self, response: str) -> str:
        """Remove <think></think> tags from the response."""
        import re
        # Remove everything between <think> and </think> tags (including the tags)
        cleaned = re.sub(r'<think>.*?</think>\s*', '', response, flags=re.DOTALL)
        return cleaned.strip()

    def _extract_space_from_username(self, username: Optional[str]) -> Optional[str]:
        """Extract space from username following chatBIS convention."""
        if not username:
            return None

        # Common patterns: username -> SPACE mapping
        # This can be customized based on your openBIS setup
        return username.upper().replace('.', '_').replace('-', '_')
