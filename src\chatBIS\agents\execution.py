#!/usr/bin/env python3
"""
Execution & Confirmation Agent for chatBIS.

This agent executes ActionRequest objects by mapping them to the appropriate pybis tools,
manages user confirmation for write operations, and returns structured results.
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..models import ActionRequest, ExecutionResult, ActionType, EntityType
from ..tools import PyBISToolManager

logger = logging.getLogger(__name__)


class ExecutionAgent:
    """Agent that executes structured ActionRequest objects using pybis tools."""
    
    def __init__(self, tool_manager: PyBISToolManager):
        """
        Initialize the Execution Agent.
        
        Args:
            tool_manager: PyBIS tool manager instance
        """
        self.tool_manager = tool_manager
        
        # Mapping of actions to tool names (using actual tool names from pybis_tools.py)
        self.action_tool_mapping = {
            # GET operations
            (ActionType.GET, EntityType.SAMPLE): "get_sample",
            (ActionType.GET, EntityType.EXPERIMENT): "get_experiment",
            (ActionType.GET, EntityType.DATASET): "get_dataset",
            (ActionType.GET, EntityType.PROJECT): "get_project",
            (ActionType.GET, EntityType.SPACE): "get_space",

            # LIST operations - using actual tool names
            (ActionType.LIST, EntityType.SAMPLE): "list_samples",
            (ActionType.LIST, EntityType.EXPERIMENT): "list_experiments",
            (ActionType.LIST, EntityType.DATASET): "list_datasets",
            (ActionType.LIST, EntityType.PROJECT): "list_projects",
            (ActionType.LIST, EntityType.SPACE): "list_spaces",

            # CREATE operations - using actual tool names
            (ActionType.CREATE, EntityType.SAMPLE): "create_sample",
            (ActionType.CREATE, EntityType.EXPERIMENT): "create_experiment",
            (ActionType.CREATE, EntityType.DATASET): "create_dataset",
            (ActionType.CREATE, EntityType.PROJECT): "create_project",
            (ActionType.CREATE, EntityType.SPACE): "create_space",

            # UPDATE operations - using actual property setting tools
            (ActionType.UPDATE, EntityType.SAMPLE): "update_sample",  # This exists
            (ActionType.UPDATE, EntityType.EXPERIMENT): "experiment_set_properties",
            (ActionType.UPDATE, EntityType.DATASET): "dataset_set_properties",
            (ActionType.UPDATE, EntityType.PROJECT): "project_save",  # Projects don't have set_properties
        }
    
    def execute_request(self, action_request: ActionRequest, 
                       user_confirmed: bool = False) -> ExecutionResult:
        """
        Execute an ActionRequest, handling confirmation for write operations.
        
        Args:
            action_request: The structured request to execute
            user_confirmed: Whether user has confirmed write operations
            
        Returns:
            ExecutionResult with status, data, or confirmation request
        """
        start_time = time.time()
        
        try:
            # Check if connection is available
            if not self.tool_manager.is_connected():
                return ExecutionResult(
                    status="error",
                    error="Not connected to openBIS. Please connect first.",
                    timestamp=datetime.now().isoformat()
                )
            
            # Separate read and write operations
            read_actions = []
            write_actions = []
            
            for action in action_request.actions:
                if action.action in [ActionType.GET, ActionType.LIST]:
                    read_actions.append(action)
                else:
                    write_actions.append(action)
            
            # If there are write operations and user hasn't confirmed, request confirmation
            if write_actions and not user_confirmed:
                confirmation_message = self._generate_confirmation_message(write_actions)
                return ExecutionResult(
                    status="confirmation_needed",
                    confirmation_required=True,
                    confirmation_message=confirmation_message,
                    pending_actions=write_actions,
                    timestamp=datetime.now().isoformat()
                )
            
            # Execute all actions
            results = []
            for action in action_request.actions:
                result = self._execute_single_action(action)
                results.append(result)
            
            # Aggregate results
            execution_time = time.time() - start_time
            
            # Check if any actions failed
            errors = [r for r in results if r.get("status") == "error"]
            if errors:
                return ExecutionResult(
                    status="error",
                    error=f"Some actions failed: {'; '.join([e.get('error', 'Unknown error') for e in errors])}",
                    data=results,
                    execution_time=execution_time,
                    actions_executed=len(results),
                    timestamp=datetime.now().isoformat()
                )
            
            return ExecutionResult(
                status="success",
                message="All actions executed successfully",
                data=results,
                execution_time=execution_time,
                actions_executed=len(results),
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"Error executing ActionRequest: {e}")
            return ExecutionResult(
                status="error",
                error=f"Execution failed: {str(e)}",
                execution_time=time.time() - start_time,
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_single_action(self, action) -> Dict[str, Any]:
        """Execute a single action and return the result."""
        try:
            # Get the appropriate tool
            tool_key = (action.action, action.entity)
            tool_name = self.action_tool_mapping.get(tool_key)
            
            if not tool_name:
                return {
                    "status": "error",
                    "error": f"No tool mapping found for {action.action} {action.entity}",
                    "action": action.dict()
                }
            
            # Find the tool
            tool = None
            for t in self.tool_manager.get_tools():
                if t.name == tool_name:
                    tool = t
                    break
            
            if not tool:
                return {
                    "status": "error", 
                    "error": f"Tool {tool_name} not found",
                    "action": action.dict()
                }
            
            # Prepare tool arguments
            tool_args = self._prepare_tool_arguments(action, tool_name)
            
            # Execute the tool
            logger.info(f"Executing tool {tool_name} with args: {tool_args}")
            result = tool.func(tool_args)
            
            return {
                "status": "success",
                "action": action.dict(),
                "tool_used": tool_name,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Error executing single action: {e}")
            return {
                "status": "error",
                "error": str(e),
                "action": action.dict()
            }
    
    def _prepare_tool_arguments(self, action, tool_name: str) -> str:
        """Prepare arguments for tool execution based on action type."""
        args = []

        if action.action in [ActionType.GET, ActionType.LIST]:
            # For retrieval operations, use lookup_criteria
            criteria = action.lookup_criteria
            if not criteria:
                return ""

            # Map to the parameter names expected by existing tools
            if criteria.identifier:
                # Different tools expect different parameter names for identifiers
                if "sample" in tool_name:
                    args.append(f"sample_identifier={criteria.identifier}")
                elif "experiment" in tool_name:
                    args.append(f"experiment_identifier={criteria.identifier}")
                elif "project" in tool_name:
                    args.append(f"project_identifier={criteria.identifier}")
                elif "dataset" in tool_name:
                    args.append(f"dataset_identifier={criteria.identifier}")
                elif "space" in tool_name:
                    args.append(f"space_code={criteria.identifier}")
                else:
                    args.append(f"identifier={criteria.identifier}")

            if criteria.space:
                args.append(f"space={criteria.space}")
            if criteria.project:
                args.append(f"project={criteria.project}")
            if criteria.experiment:
                args.append(f"experiment={criteria.experiment}")
            if criteria.type:
                # Map type to the correct parameter name
                if "sample" in tool_name:
                    args.append(f"sample_type={criteria.type}")
                elif "experiment" in tool_name:
                    args.append(f"experiment_type={criteria.type}")
                elif "dataset" in tool_name:
                    args.append(f"dataset_type={criteria.type}")
                else:
                    args.append(f"type={criteria.type}")
            if criteria.properties:
                for key, value in criteria.properties.items():
                    args.append(f"{key}={value}")
            if criteria.count:
                args.append(f"limit={criteria.count}")  # Most tools use 'limit' not 'count'

        elif action.action in [ActionType.CREATE, ActionType.UPDATE]:
            # For write operations, use payload
            payload = action.payload
            if not payload:
                return ""

            if payload.code:
                args.append(f"code={payload.code}")
            if payload.type:
                # Map type to the correct parameter name for creation
                if "sample" in tool_name:
                    args.append(f"sample_type={payload.type}")
                elif "experiment" in tool_name:
                    args.append(f"experiment_type={payload.type}")
                elif "dataset" in tool_name:
                    args.append(f"dataset_type={payload.type}")
                else:
                    args.append(f"type={payload.type}")
            if payload.space:
                args.append(f"space={payload.space}")
            if payload.project:
                args.append(f"project={payload.project}")
            if payload.experiment:
                args.append(f"experiment={payload.experiment}")
            if payload.description:
                args.append(f"description={payload.description}")
            if payload.properties:
                # Format properties as expected by tools
                props_items = []
                for k, v in payload.properties.items():
                    props_items.append(f"{k}={v}")
                if props_items:
                    args.append(f"properties={{{', '.join(props_items)}}}")
            if payload.parents:
                args.append(f"parents={payload.parents}")

            # For UPDATE operations, also need identifier
            if action.action == ActionType.UPDATE and action.lookup_criteria and action.lookup_criteria.identifier:
                if "sample" in tool_name:
                    args.insert(0, f"sample_identifier={action.lookup_criteria.identifier}")
                elif "experiment" in tool_name:
                    args.insert(0, f"identifier={action.lookup_criteria.identifier}")
                elif "dataset" in tool_name:
                    args.insert(0, f"identifier={action.lookup_criteria.identifier}")
                else:
                    args.insert(0, f"identifier={action.lookup_criteria.identifier}")

        return ", ".join(args)
    
    def _generate_confirmation_message(self, write_actions: List) -> str:
        """Generate a human-readable confirmation message for write operations."""
        messages = []
        
        for action in write_actions:
            if action.action == ActionType.CREATE:
                entity_name = action.entity.value.lower()
                code = action.payload.code if action.payload else "unknown"
                messages.append(f"CREATE a new {entity_name} with code '{code}'")
            elif action.action == ActionType.UPDATE:
                entity_name = action.entity.value.lower()
                identifier = action.lookup_criteria.identifier if action.lookup_criteria else "unknown"
                messages.append(f"UPDATE {entity_name} '{identifier}'")
        
        if len(messages) == 1:
            return f"I am about to {messages[0]}. Do you want to proceed? (yes/no)"
        else:
            action_list = "\n- ".join(messages)
            return f"I am about to perform the following actions:\n- {action_list}\n\nDo you want to proceed? (yes/no)"
