"""
Models module for chatBIS.

This module contains Pydantic models and data structures used throughout
the chatBIS application, particularly for structured interactions with openBIS.
"""

# Import will be available once entity.py is created
try:
    from .entity import (
        ActionType,
        EntityType,
        LookupCriteria,
        Payload,
        Action,
        ActionRequest,
        ClarificationRequest,
        ExecutionResult
    )

    __all__ = [
        "ActionType",
        "EntityType",
        "LookupCriteria",
        "Payload",
        "Action",
        "ActionRequest",
        "ClarificationRequest",
        "ExecutionResult"
    ]
except ImportError:
    # Models not yet implemented
    __all__ = []
