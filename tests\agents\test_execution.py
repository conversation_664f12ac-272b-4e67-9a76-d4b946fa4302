#!/usr/bin/env python3
"""
Unit tests for ExecutionAgent.

Tests the execution of ActionRequest objects using pybis tools,
including confirmation workflows and error handling.
"""

import pytest
from unittest.mock import Mock, MagicMock
from langchain_core.tools import Tool

from src.chatBIS.agents.execution import ExecutionAgent
from src.chatBIS.models import ActionRequest, Action, ActionType, EntityType, LookupCriteria, Payload
from src.chatBIS.tools import PyBISToolManager


class TestExecutionAgent:
    """Test cases for ExecutionAgent."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_tool_manager = Mock(spec=PyBISToolManager)
        self.agent = ExecutionAgent(self.mock_tool_manager)
        
        # Mock connection status
        self.mock_tool_manager.is_connected.return_value = True
    
    def test_execute_get_sample_request(self):
        """Test executing a GET sample request."""
        # Create test action request
        action = Action(
            action=ActionType.GET,
            entity=EntityType.SAMPLE,
            lookup_criteria=LookupCriteria(identifier="SAMPLE_001")
        )
        request = ActionRequest(actions=[action])
        
        # Mock tool
        mock_tool = Mock(spec=Tool)
        mock_tool.name = "get_sample"
        mock_tool.func.return_value = "Sample SAMPLE_001 details"
        self.mock_tool_manager.get_tools.return_value = [mock_tool]
        
        result = self.agent.execute_request(request)
        
        assert result.status == "success"
        assert result.actions_executed == 1
        assert len(result.data) == 1
        assert result.data[0]["status"] == "success"
        assert result.data[0]["tool_used"] == "get_sample"
    
    def test_execute_list_samples_request(self):
        """Test executing a LIST samples request."""
        action = Action(
            action=ActionType.LIST,
            entity=EntityType.SAMPLE,
            lookup_criteria=LookupCriteria(space="MY_SPACE", type="YEAST")
        )
        request = ActionRequest(actions=[action])
        
        mock_tool = Mock(spec=Tool)
        mock_tool.name = "get_samples"
        mock_tool.func.return_value = "Found 3 YEAST samples in MY_SPACE"
        self.mock_tool_manager.get_tools.return_value = [mock_tool]
        
        result = self.agent.execute_request(request)
        
        assert result.status == "success"
        mock_tool.func.assert_called_once()
        # Verify tool arguments contain space and type
        call_args = mock_tool.func.call_args[0][0]
        assert "space=MY_SPACE" in call_args
        assert "type=YEAST" in call_args
    
    def test_execute_create_sample_requires_confirmation(self):
        """Test that CREATE operations require confirmation."""
        action = Action(
            action=ActionType.CREATE,
            entity=EntityType.SAMPLE,
            payload=Payload(code="NEW_SAMPLE", type="YEAST", space="MY_SPACE")
        )
        request = ActionRequest(actions=[action])
        
        result = self.agent.execute_request(request, user_confirmed=False)
        
        assert result.status == "confirmation_needed"
        assert result.confirmation_required is True
        assert "CREATE a new sample" in result.confirmation_message
        assert len(result.pending_actions) == 1
    
    def test_execute_create_sample_with_confirmation(self):
        """Test executing CREATE operation after user confirmation."""
        action = Action(
            action=ActionType.CREATE,
            entity=EntityType.SAMPLE,
            payload=Payload(code="NEW_SAMPLE", type="YEAST", space="MY_SPACE")
        )
        request = ActionRequest(actions=[action])
        
        mock_tool = Mock(spec=Tool)
        mock_tool.name = "new_sample"
        mock_tool.func.return_value = "Created sample NEW_SAMPLE"
        self.mock_tool_manager.get_tools.return_value = [mock_tool]
        
        result = self.agent.execute_request(request, user_confirmed=True)
        
        assert result.status == "success"
        mock_tool.func.assert_called_once()
        call_args = mock_tool.func.call_args[0][0]
        assert "code=NEW_SAMPLE" in call_args
        assert "type=YEAST" in call_args
        assert "space=MY_SPACE" in call_args
    
    def test_execute_update_sample_requires_confirmation(self):
        """Test that UPDATE operations require confirmation."""
        action = Action(
            action=ActionType.UPDATE,
            entity=EntityType.SAMPLE,
            lookup_criteria=LookupCriteria(identifier="SAMPLE_001"),
            payload=Payload(properties={"NOTES": "Updated notes"})
        )
        request = ActionRequest(actions=[action])
        
        result = self.agent.execute_request(request, user_confirmed=False)
        
        assert result.status == "confirmation_needed"
        assert "UPDATE sample 'SAMPLE_001'" in result.confirmation_message
    
    def test_execute_mixed_read_write_operations(self):
        """Test executing mixed read and write operations."""
        actions = [
            Action(
                action=ActionType.GET,
                entity=EntityType.SAMPLE,
                lookup_criteria=LookupCriteria(identifier="SAMPLE_001")
            ),
            Action(
                action=ActionType.CREATE,
                entity=EntityType.SAMPLE,
                payload=Payload(code="NEW_SAMPLE", type="YEAST", space="MY_SPACE")
            )
        ]
        request = ActionRequest(actions=actions)
        
        result = self.agent.execute_request(request, user_confirmed=False)
        
        # Should require confirmation due to CREATE operation
        assert result.status == "confirmation_needed"
        assert len(result.pending_actions) == 1  # Only the CREATE action
        assert result.pending_actions[0]["action"] == "CREATE"
    
    def test_execute_not_connected_error(self):
        """Test handling when not connected to openBIS."""
        self.mock_tool_manager.is_connected.return_value = False
        
        action = Action(
            action=ActionType.GET,
            entity=EntityType.SAMPLE,
            lookup_criteria=LookupCriteria(identifier="SAMPLE_001")
        )
        request = ActionRequest(actions=[action])
        
        result = self.agent.execute_request(request)
        
        assert result.status == "error"
        assert "Not connected to openBIS" in result.error
    
    def test_execute_tool_not_found_error(self):
        """Test handling when required tool is not found."""
        action = Action(
            action=ActionType.GET,
            entity=EntityType.SAMPLE,
            lookup_criteria=LookupCriteria(identifier="SAMPLE_001")
        )
        request = ActionRequest(actions=[action])
        
        # Return empty tools list
        self.mock_tool_manager.get_tools.return_value = []
        
        result = self.agent.execute_request(request, user_confirmed=True)
        
        assert result.status == "error"
        assert "Tool get_sample not found" in result.error
    
    def test_execute_tool_exception_handling(self):
        """Test handling of tool execution exceptions."""
        action = Action(
            action=ActionType.GET,
            entity=EntityType.SAMPLE,
            lookup_criteria=LookupCriteria(identifier="SAMPLE_001")
        )
        request = ActionRequest(actions=[action])
        
        mock_tool = Mock(spec=Tool)
        mock_tool.name = "get_sample"
        mock_tool.func.side_effect = Exception("pybis connection error")
        self.mock_tool_manager.get_tools.return_value = [mock_tool]
        
        result = self.agent.execute_request(request, user_confirmed=True)
        
        assert result.status == "error"
        assert "pybis connection error" in result.error
    
    def test_prepare_tool_arguments_get_operation(self):
        """Test preparation of tool arguments for GET operations."""
        action = Action(
            action=ActionType.GET,
            entity=EntityType.SAMPLE,
            lookup_criteria=LookupCriteria(identifier="SAMPLE_001")
        )
        
        args = self.agent._prepare_tool_arguments(action, "get_sample")
        
        assert "identifier=SAMPLE_001" in args
    
    def test_prepare_tool_arguments_list_operation(self):
        """Test preparation of tool arguments for LIST operations."""
        action = Action(
            action=ActionType.LIST,
            entity=EntityType.SAMPLE,
            lookup_criteria=LookupCriteria(
                space="MY_SPACE",
                type="YEAST",
                properties={"NAME": "*test*"},
                count=10
            )
        )
        
        args = self.agent._prepare_tool_arguments(action, "get_samples")
        
        assert "space=MY_SPACE" in args
        assert "type=YEAST" in args
        assert "NAME=*test*" in args
        assert "count=10" in args
    
    def test_prepare_tool_arguments_create_operation(self):
        """Test preparation of tool arguments for CREATE operations."""
        action = Action(
            action=ActionType.CREATE,
            entity=EntityType.SAMPLE,
            payload=Payload(
                code="NEW_SAMPLE",
                type="YEAST",
                space="MY_SPACE",
                properties={"NOTES": "Test sample"},
                parents=["PARENT_001"]
            )
        )
        
        args = self.agent._prepare_tool_arguments(action, "new_sample")
        
        assert "code=NEW_SAMPLE" in args
        assert "type=YEAST" in args
        assert "space=MY_SPACE" in args
        assert "NOTES=Test sample" in args
        assert "parents=['PARENT_001']" in args
    
    def test_generate_confirmation_message_single_action(self):
        """Test confirmation message generation for single action."""
        actions = [Action(
            action=ActionType.CREATE,
            entity=EntityType.SAMPLE,
            payload=Payload(code="NEW_SAMPLE")
        )]
        
        message = self.agent._generate_confirmation_message(actions)
        
        assert "CREATE a new sample with code 'NEW_SAMPLE'" in message
        assert "Do you want to proceed? (yes/no)" in message
    
    def test_generate_confirmation_message_multiple_actions(self):
        """Test confirmation message generation for multiple actions."""
        actions = [
            Action(
                action=ActionType.CREATE,
                entity=EntityType.SAMPLE,
                payload=Payload(code="NEW_SAMPLE")
            ),
            Action(
                action=ActionType.UPDATE,
                entity=EntityType.EXPERIMENT,
                lookup_criteria=LookupCriteria(identifier="EXP_001")
            )
        ]
        
        message = self.agent._generate_confirmation_message(actions)
        
        assert "CREATE a new sample" in message
        assert "UPDATE experiment 'EXP_001'" in message
        assert "Do you want to proceed? (yes/no)" in message
