#!/usr/bin/env python3
"""
Pydantic models for structured openBIS interactions in chatBIS.

This module defines the core data structures used for translating natural language
queries into structured JSON format for execution with pybis, and for handling
the results of those executions.
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, field_validator, model_validator


class ActionType(str, Enum):
    """Types of actions that can be performed on openBIS entities."""
    GET = "GET"          # Retrieve a single entity by identifier
    LIST = "LIST"        # Search/list multiple entities
    CREATE = "CREATE"    # Create a new entity
    UPDATE = "UPDATE"    # Update an existing entity


class EntityType(str, Enum):
    """Types of entities in openBIS."""
    SAMPLE = "SAMPLE"
    EXPERIMENT = "EXPERIMENT"
    DATASET = "DATASET"
    PROJECT = "PROJECT"
    SPACE = "SPACE"
    COLLECTION = "COLLECTION"
    OBJECT = "OBJECT"


class LookupCriteria(BaseModel):
    """Criteria for looking up entities in openBIS."""
    # For GET/UPDATE operations - unique identifier
    identifier: Optional[str] = Field(None, description="Full identifier like /SPACE/PROJECT/OBJECT or permId")

    # For LIST operations - search parameters
    space: Optional[str] = Field(None, description="Space code to search within")
    project: Optional[str] = Field(None, description="Project identifier to search within")
    experiment: Optional[str] = Field(None, description="Experiment identifier to search within")
    type: Optional[str] = Field(None, description="Entity type code to filter by")
    properties: Optional[Dict[str, str]] = Field(None, description="Property filters for search")

    # Additional search parameters
    tags: Optional[List[str]] = Field(None, description="Tags to filter by")
    start_with: Optional[str] = Field(None, description="Code prefix to filter by")

    # Date/time filters
    registration_date_from: Optional[str] = Field(None, description="Filter by registration date from (ISO format)")
    registration_date_to: Optional[str] = Field(None, description="Filter by registration date to (ISO format)")
    modification_date_from: Optional[str] = Field(None, description="Filter by modification date from (ISO format)")
    modification_date_to: Optional[str] = Field(None, description="Filter by modification date to (ISO format)")

    # Pagination
    count: Optional[int] = Field(None, description="Maximum number of results to return")
    start: Optional[int] = Field(None, description="Starting index for pagination")

    @field_validator('identifier')
    @classmethod
    def validate_identifier(cls, v):
        """Validate identifier format."""
        if v and not (v.startswith('/') or len(v) == 20):  # permId is typically 20 chars
            # Allow flexibility for various identifier formats
            pass
        return v


class Payload(BaseModel):
    """Data payload for CREATE/UPDATE operations."""
    # Common fields for all entities
    code: Optional[str] = Field(None, description="Entity code/name")
    type: Optional[str] = Field(None, description="Entity type code")
    description: Optional[str] = Field(None, description="Entity description")
    properties: Optional[Dict[str, Any]] = Field(None, description="Entity properties")

    # Relationship fields
    space: Optional[str] = Field(None, description="Space identifier")
    project: Optional[str] = Field(None, description="Project identifier")
    experiment: Optional[str] = Field(None, description="Experiment identifier")
    sample: Optional[str] = Field(None, description="Sample identifier")

    # Sample-specific fields
    parents: Optional[List[str]] = Field(None, description="Parent sample identifiers")
    children: Optional[List[str]] = Field(None, description="Child sample identifiers")

    # Dataset-specific fields
    files: Optional[List[str]] = Field(None, description="File paths for dataset")
    kind: Optional[str] = Field(None, description="Dataset kind (PHYSICAL, LINK, CONTAINER)")

    # Additional metadata
    tags: Optional[List[str]] = Field(None, description="Tags to assign")
    attachments: Optional[List[str]] = Field(None, description="Attachment file paths")


class Action(BaseModel):
    """A single action to be performed on an openBIS entity."""
    action: ActionType = Field(..., description="Type of action to perform")
    entity: EntityType = Field(..., description="Type of entity to act upon")
    lookup_criteria: Optional[LookupCriteria] = Field(None, description="Criteria for finding the entity")
    payload: Optional[Payload] = Field(None, description="Data for CREATE/UPDATE operations")

    @model_validator(mode='after')
    def validate_action_requirements(self):
        """Validate that required fields are provided based on action type."""
        action = self.action

        # Validate lookup_criteria requirements
        if action in [ActionType.GET, ActionType.UPDATE, ActionType.LIST] and not self.lookup_criteria:
            raise ValueError(f"lookup_criteria is required for {action} operations")

        # Validate payload requirements
        if action in [ActionType.CREATE, ActionType.UPDATE] and not self.payload:
            raise ValueError(f"payload is required for {action} operations")

        return self


class ActionRequest(BaseModel):
    """Complete request containing one or more actions to execute."""
    actions: List[Action] = Field(..., min_items=1, description="List of actions to execute")

    # Metadata
    user_query: Optional[str] = Field(None, description="Original user query for context")
    session_id: Optional[str] = Field(None, description="Session identifier")
    timestamp: Optional[str] = Field(None, description="Request timestamp")

    class Config:
        """Pydantic configuration."""
        json_encoders = {
            ActionType: lambda v: v.value,
            EntityType: lambda v: v.value
        }


class ClarificationRequest(BaseModel):
    """Request for clarification when information is missing."""
    status: str = Field("clarification_needed", description="Status indicator")
    message: str = Field(..., description="Clarification message for the user")
    missing_fields: Optional[List[str]] = Field(None, description="List of missing required fields")
    suggestions: Optional[List[str]] = Field(None, description="Suggested values or options")


class ExecutionResult(BaseModel):
    """Result of executing an ActionRequest."""
    status: str = Field(..., description="Execution status: success, error, confirmation_needed")
    message: Optional[str] = Field(None, description="Human-readable message")
    data: Optional[Any] = Field(None, description="Execution result data")
    error: Optional[str] = Field(None, description="Error message if status is error")

    # For confirmation workflow
    confirmation_required: Optional[bool] = Field(None, description="Whether user confirmation is needed")
    confirmation_message: Optional[str] = Field(None, description="Message to show user for confirmation")
    pending_actions: Optional[List[Action]] = Field(None, description="Actions waiting for confirmation")

    # Metadata
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    actions_executed: Optional[int] = Field(None, description="Number of actions executed")
    timestamp: Optional[str] = Field(None, description="Execution timestamp")