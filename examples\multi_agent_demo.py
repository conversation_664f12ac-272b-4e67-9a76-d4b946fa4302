#!/usr/bin/env python3
"""
Demo script for the new multi-agent workflow in chatBIS.

This script demonstrates the structured workflow with Intent Parser,
Execution Agent, and Explainer Agent working together.
"""

import sys
import os
import json
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from chatBIS.models import ActionRequest, Action, ActionType, EntityType, LookupCriteria, Payload
from chatBIS.agents import IntentParserAgent, ExecutionAgent, ExplainerAgent


def demo_pydantic_models():
    """Demonstrate the Pydantic models."""
    print("=== Pydantic Models Demo ===")

    # Create a sample ActionRequest
    action = Action(
        action=ActionType.LIST,
        entity=EntityType.SAMPLE,
        lookup_criteria=LookupCriteria(
            space="MY_SPACE",
            type="YEAST",
            count=10
        )
    )

    request = ActionRequest(
        actions=[action],
        user_query="List 10 YEAST samples in MY_SPACE"
    )

    print("Created ActionRequest:")
    print(json.dumps(request.model_dump(), indent=2))
    print()

def demo_intent_parser():
    """Demonstrate the Intent Parser Agent."""
    print("=== Intent Parser Agent Demo ===")

    # Mock LLM for demo
    class MockLLM:
        def invoke(self, messages):
            class MockResponse:
                content = json.dumps({
                    "actions": [{
                        "action": "LIST",
                        "entity": "SAMPLE",
                        "lookup_criteria": {
                            "space": "MY_SPACE",
                            "type": "YEAST"
                        }
                    }]
                })
            return MockResponse()

    parser = IntentParserAgent(MockLLM())
    result = parser.parse_intent("List all YEAST samples in MY_SPACE")

    print("Parsed intent:")
    if hasattr(result, 'model_dump'):
        print(json.dumps(result.model_dump(), indent=2))
    else:
        print(result)
    print()


def demo_create_sample_action():
    """Demonstrate creating a sample action."""
    print("=== Create Sample Action Demo ===")

    create_action = Action(
        action=ActionType.CREATE,
        entity=EntityType.SAMPLE,
        payload=Payload(
            code="DEMO_SAMPLE_001",
            type="YEAST",
            space="DEMO_SPACE",
            properties={"NOTES": "Created by multi-agent demo"}
        )
    )

    request = ActionRequest(
        actions=[create_action],
        user_query="Create a new YEAST sample called DEMO_SAMPLE_001"
    )

    print("Create sample request:")
    print(json.dumps(request.model_dump(), indent=2))
    print()

def demo_validation():
    """Demonstrate Pydantic validation."""
    print("=== Validation Demo ===")

    try:
        # This should fail validation - CREATE without payload
        invalid_action = Action(
            action=ActionType.CREATE,
            entity=EntityType.SAMPLE
            # Missing payload - should trigger validation error
        )
        print("This shouldn't print - validation should fail")
    except Exception as e:
        print(f"Validation correctly caught error: {e}")

    try:
        # This should fail validation - GET without lookup_criteria
        invalid_action = Action(
            action=ActionType.GET,
            entity=EntityType.SAMPLE
            # Missing lookup_criteria - should trigger validation error
        )
        print("This shouldn't print - validation should fail")
    except Exception as e:
        print(f"Validation correctly caught error: {e}")

    print()

def main():
    """Run all demos."""
    print("Multi-Agent Workflow Demo for chatBIS")
    print("=" * 50)
    print()

    try:
        demo_pydantic_models()
        demo_intent_parser()
        demo_create_sample_action()
        demo_validation()

        print("=== Summary ===")
        print("✅ Pydantic models working correctly")
        print("✅ Intent Parser Agent functional")
        print("✅ Action creation and validation working")
        print("✅ Multi-agent workflow components ready")
        print()
        print("The new structured workflow is ready for integration!")

    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
