#!/usr/bin/env python3
"""
Integration tests for the complete multi-agent workflow.

Tests the end-to-end flow from natural language input through all three agents
(IntentParser, Execution, Explainer) including user confirmation scenarios.
"""

import json
import pytest
from unittest.mock import Mock, MagicMock, patch
from langchain_core.messages import AIMessage
from langchain_core.tools import Tool

from src.chatBIS.query.conversation_engine import ConversationEngine, ConversationState
from src.chatBIS.models import ActionType, EntityType


class TestMultiAgentIntegration:
    """Integration tests for the complete multi-agent workflow."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Mock the dependencies
        with patch('src.chatBIS.query.conversation_engine.RAGQueryEngine'), \
             patch('src.chatBIS.query.conversation_engine.PyBISToolManager'), \
             patch('src.chatBIS.query.conversation_engine.ChatOllama'), \
             patch('src.chatBIS.query.conversation_engine.SqliteSaver'):
            
            self.engine = ConversationEngine(data_dir="test_data", model="test_model")
            
            # Mock the LLM for all agents
            self.mock_llm = Mock()
            self.engine.llm = self.mock_llm
            self.engine.intent_parser.llm = self.mock_llm
            self.engine.explainer_agent.llm = self.mock_llm
            
            # Mock tool manager
            self.engine.tool_manager.is_connected.return_value = True
    
    def test_end_to_end_get_sample_flow(self):
        """Test complete flow for GET sample request."""
        # Mock intent parser response
        intent_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "GET",
                "entity": "SAMPLE",
                "lookup_criteria": {"identifier": "SAMPLE_001"}
            }]
        }))
        
        # Mock explainer response
        explainer_response = AIMessage(content="Here are the details for sample SAMPLE_001: Type is YEAST, created on 2024-01-15.")
        
        # Set up LLM to return different responses for different calls
        self.mock_llm.invoke.side_effect = [intent_response, explainer_response]
        
        # Mock tool execution
        mock_tool = Mock(spec=Tool)
        mock_tool.name = "get_sample"
        mock_tool.func.return_value = "Sample SAMPLE_001: Type=YEAST, Created=2024-01-15"
        self.engine.tool_manager.get_tools.return_value = [mock_tool]
        
        # Execute the workflow
        response, session_id, metadata = self.engine.chat("Show me sample SAMPLE_001")
        
        # Verify the response
        assert "details for sample SAMPLE_001" in response
        assert "YEAST" in response
        assert metadata["decision"] == "structured_pybis"
        
        # Verify tool was called
        mock_tool.func.assert_called_once()
    
    def test_end_to_end_create_sample_with_confirmation(self):
        """Test complete flow for CREATE sample with confirmation workflow."""
        # Mock intent parser response
        intent_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "CREATE",
                "entity": "SAMPLE",
                "payload": {
                    "code": "NEW_SAMPLE_01",
                    "type": "YEAST",
                    "space": "MY_SPACE"
                }
            }]
        }))
        
        self.mock_llm.invoke.return_value = intent_response
        
        # Mock tool (won't be called in first request due to confirmation)
        mock_tool = Mock(spec=Tool)
        mock_tool.name = "new_sample"
        self.engine.tool_manager.get_tools.return_value = [mock_tool]
        
        # First request - should ask for confirmation
        response1, session_id, metadata1 = self.engine.chat("Create a new YEAST sample called NEW_SAMPLE_01 in MY_SPACE")
        
        assert "CREATE a new sample" in response1
        assert "Do you want to proceed?" in response1
        assert metadata1["decision"] == "structured_pybis"
        
        # Mock tool execution for confirmed request
        mock_tool.func.return_value = "Successfully created sample NEW_SAMPLE_01"
        
        # Mock explainer response for success
        success_response = AIMessage(content="Success! I created the new sample NEW_SAMPLE_01 in MY_SPACE.")
        self.mock_llm.invoke.return_value = success_response
        
        # Second request - user confirms
        response2, _, metadata2 = self.engine.chat("yes", session_id)
        
        assert "Success!" in response2
        assert "NEW_SAMPLE_01" in response2
        
        # Verify tool was called after confirmation
        mock_tool.func.assert_called_once()
    
    def test_end_to_end_create_sample_cancelled(self):
        """Test CREATE sample workflow when user cancels."""
        # Mock intent parser response
        intent_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "CREATE",
                "entity": "SAMPLE",
                "payload": {"code": "NEW_SAMPLE_01", "type": "YEAST", "space": "MY_SPACE"}
            }]
        }))
        
        self.mock_llm.invoke.return_value = intent_response
        
        # First request - should ask for confirmation
        response1, session_id, _ = self.engine.chat("Create a new sample")
        assert "Do you want to proceed?" in response1
        
        # Second request - user cancels
        response2, _, _ = self.engine.chat("no", session_id)
        assert "Operation cancelled" in response2
    
    def test_end_to_end_clarification_needed(self):
        """Test flow when clarification is needed."""
        # Mock intent parser response requesting clarification
        clarification_response = AIMessage(content=json.dumps({
            "status": "clarification_needed",
            "message": "What type should the new sample have?",
            "missing_fields": ["type"],
            "suggestions": ["YEAST", "BACTERIA"]
        }))
        
        self.mock_llm.invoke.return_value = clarification_response
        
        response, session_id, metadata = self.engine.chat("Create a new sample called TEST01")
        
        assert "What type should the new sample have?" in response
        assert "YEAST, BACTERIA" in response
        assert metadata["decision"] == "structured_pybis"
    
    def test_end_to_end_list_samples_flow(self):
        """Test complete flow for LIST samples request."""
        # Mock intent parser response
        intent_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "LIST",
                "entity": "SAMPLE",
                "lookup_criteria": {"space": "MY_SPACE", "type": "YEAST"}
            }]
        }))
        
        # Mock explainer response
        explainer_response = AIMessage(content="I found 3 YEAST samples in MY_SPACE:\n• SAMPLE_001\n• SAMPLE_002\n• SAMPLE_003")
        
        self.mock_llm.invoke.side_effect = [intent_response, explainer_response]
        
        # Mock tool execution
        mock_tool = Mock(spec=Tool)
        mock_tool.name = "get_samples"
        mock_tool.func.return_value = "Found 3 samples: SAMPLE_001, SAMPLE_002, SAMPLE_003"
        self.engine.tool_manager.get_tools.return_value = [mock_tool]
        
        response, session_id, metadata = self.engine.chat("List all YEAST samples in MY_SPACE")
        
        assert "I found 3 YEAST samples" in response
        assert "SAMPLE_001" in response
        assert metadata["decision"] == "structured_pybis"
        
        # Verify tool arguments
        call_args = mock_tool.func.call_args[0][0]
        assert "space=MY_SPACE" in call_args
        assert "type=YEAST" in call_args
    
    def test_end_to_end_error_handling(self):
        """Test error handling in the complete workflow."""
        # Mock intent parser response
        intent_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "GET",
                "entity": "SAMPLE",
                "lookup_criteria": {"identifier": "NONEXISTENT_SAMPLE"}
            }]
        }))
        
        self.mock_llm.invoke.return_value = intent_response
        
        # Mock tool to raise exception
        mock_tool = Mock(spec=Tool)
        mock_tool.name = "get_sample"
        mock_tool.func.side_effect = Exception("Sample not found")
        self.engine.tool_manager.get_tools.return_value = [mock_tool]
        
        response, session_id, metadata = self.engine.chat("Show me NONEXISTENT_SAMPLE")
        
        assert "Sample not found" in response or "error" in response.lower()
    
    def test_end_to_end_not_connected_error(self):
        """Test handling when not connected to openBIS."""
        # Mock not connected
        self.engine.tool_manager.is_connected.return_value = False
        
        # Mock intent parser response
        intent_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "GET",
                "entity": "SAMPLE",
                "lookup_criteria": {"identifier": "SAMPLE_001"}
            }]
        }))
        
        self.mock_llm.invoke.return_value = intent_response
        
        response, session_id, metadata = self.engine.chat("Show me SAMPLE_001")
        
        assert "Not connected to openBIS" in response
    
    def test_end_to_end_invalid_json_from_intent_parser(self):
        """Test handling of invalid JSON from intent parser."""
        # Mock invalid JSON response
        invalid_response = AIMessage(content="This is not valid JSON")
        self.mock_llm.invoke.return_value = invalid_response
        
        response, session_id, metadata = self.engine.chat("Show me samples")
        
        assert "trouble understanding" in response
        assert metadata["decision"] == "structured_pybis"
    
    def test_end_to_end_multiple_actions(self):
        """Test workflow with multiple actions."""
        # Mock intent parser response with multiple actions
        intent_response = AIMessage(content=json.dumps({
            "actions": [
                {
                    "action": "GET",
                    "entity": "SAMPLE",
                    "lookup_criteria": {"identifier": "SAMPLE_001"}
                },
                {
                    "action": "LIST",
                    "entity": "EXPERIMENT",
                    "lookup_criteria": {"space": "MY_SPACE"}
                }
            ]
        }))
        
        # Mock explainer response
        explainer_response = AIMessage(content="Here's SAMPLE_001 details and I found 2 experiments in MY_SPACE.")
        
        self.mock_llm.invoke.side_effect = [intent_response, explainer_response]
        
        # Mock tools
        sample_tool = Mock(spec=Tool)
        sample_tool.name = "get_sample"
        sample_tool.func.return_value = "Sample SAMPLE_001 details"
        
        experiment_tool = Mock(spec=Tool)
        experiment_tool.name = "get_experiments"
        experiment_tool.func.return_value = "Found 2 experiments"
        
        self.engine.tool_manager.get_tools.return_value = [sample_tool, experiment_tool]
        
        response, session_id, metadata = self.engine.chat("Show me SAMPLE_001 and list experiments in MY_SPACE")
        
        assert "SAMPLE_001" in response
        assert "experiments" in response
        
        # Verify both tools were called
        sample_tool.func.assert_called_once()
        experiment_tool.func.assert_called_once()
    
    def test_routing_to_rag_for_documentation_query(self):
        """Test that documentation queries are still routed to RAG."""
        # This should go to RAG, not structured workflow
        with patch.object(self.engine, 'rag_engine') as mock_rag:
            mock_rag.retrieve_relevant_chunks.return_value = [{"content": "openBIS documentation"}]
            
            # Mock RAG response
            rag_response = AIMessage(content="openBIS is a platform for managing research data.")
            self.mock_llm.invoke.return_value = rag_response
            
            response, session_id, metadata = self.engine.chat("How do I use openBIS?")
            
            assert metadata["decision"] == "rag"
            assert "openBIS is a platform" in response
