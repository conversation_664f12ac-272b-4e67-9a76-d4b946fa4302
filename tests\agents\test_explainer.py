#!/usr/bin/env python3
"""
Unit tests for ExplainerAgent.

Tests the conversion of structured execution results into natural language
responses, including success and error scenarios.
"""

import pytest
from unittest.mock import Mock
from langchain_core.messages import AIMessage

from src.chatBIS.agents.explainer import ExplainerAgent
from src.chatBIS.models import ExecutionResult, ClarificationRequest


class TestExplainerAgent:
    """Test cases for ExplainerAgent."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_llm = Mock()
        self.agent = ExplainerAgent(self.mock_llm)
    
    def test_generate_response_clarification_request(self):
        """Test generating response for clarification request."""
        clarification = ClarificationRequest(
            message="What type should the new sample have?",
            missing_fields=["type"],
            suggestions=["YEAST", "BACTERIA", "CELL_LINE"]
        )
        
        response = self.agent.generate_response(clarification, "Create a new sample")
        
        assert "What type should the new sample have?" in response
        assert "YEAST, BACTERIA, CELL_LINE" in response
    
    def test_generate_response_confirmation_needed(self):
        """Test generating response for confirmation needed."""
        result = ExecutionResult(
            status="confirmation_needed",
            confirmation_message="I am about to CREATE a new sample. Do you want to proceed? (yes/no)"
        )
        
        response = self.agent.generate_response(result, "Create a new sample")
        
        assert "CREATE a new sample" in response
        assert "Do you want to proceed?" in response
    
    def test_generate_response_error(self):
        """Test generating response for error result."""
        result = ExecutionResult(
            status="error",
            error="Sample SAMPLE_001 not found"
        )
        
        response = self.agent.generate_response(result, "Show me SAMPLE_001")
        
        assert "couldn't find the requested item" in response
    
    def test_generate_response_permission_error(self):
        """Test generating response for permission error."""
        result = ExecutionResult(
            status="error",
            error="Permission denied: insufficient access rights"
        )
        
        response = self.agent.generate_response(result, "Delete sample SAMPLE_001")
        
        assert "don't have permission" in response
    
    def test_generate_response_connection_error(self):
        """Test generating response for connection error."""
        result = ExecutionResult(
            status="error",
            error="Connection timeout to openBIS server"
        )
        
        response = self.agent.generate_response(result, "List samples")
        
        assert "trouble connecting to openBIS" in response
    
    def test_generate_response_success_with_llm(self):
        """Test generating response for successful execution using LLM."""
        result = ExecutionResult(
            status="success",
            data=[{
                "status": "success",
                "action": {"action": "LIST", "entity": "SAMPLE"},
                "result": "Found 3 samples: SAMPLE_001, SAMPLE_002, SAMPLE_003"
            }],
            actions_executed=1,
            execution_time=0.5
        )
        
        # Mock LLM response
        mock_response = AIMessage(content="I found 3 samples in your search:\n• SAMPLE_001\n• SAMPLE_002\n• SAMPLE_003")
        self.mock_llm.invoke.return_value = mock_response
        
        response = self.agent.generate_response(result, "List all samples")
        
        assert "I found 3 samples" in response
        assert "SAMPLE_001" in response
        assert "SAMPLE_002" in response
        assert "SAMPLE_003" in response
    
    def test_generate_response_success_fallback_template(self):
        """Test generating response using fallback template when LLM fails."""
        result = ExecutionResult(
            status="success",
            data=[{
                "status": "success",
                "action": {"action": "GET", "entity": "SAMPLE"},
                "result": "Sample details retrieved"
            }]
        )
        
        # Mock LLM to raise exception
        self.mock_llm.invoke.side_effect = Exception("LLM error")
        
        response = self.agent.generate_response(result, "Show me SAMPLE_001")
        
        assert "Retrieved sample details successfully" in response
    
    def test_generate_response_create_success_template(self):
        """Test template response for CREATE success."""
        result = ExecutionResult(
            status="success",
            data=[{
                "status": "success",
                "action": {"action": "CREATE", "entity": "SAMPLE"},
                "result": "Sample created successfully"
            }]
        )
        
        # Mock LLM to raise exception to test fallback
        self.mock_llm.invoke.side_effect = Exception("LLM error")
        
        response = self.agent.generate_response(result, "Create a new sample")
        
        assert "Successfully created new sample" in response
    
    def test_generate_response_update_success_template(self):
        """Test template response for UPDATE success."""
        result = ExecutionResult(
            status="success",
            data=[{
                "status": "success",
                "action": {"action": "UPDATE", "entity": "EXPERIMENT"},
                "result": "Experiment updated"
            }]
        )
        
        # Mock LLM to raise exception to test fallback
        self.mock_llm.invoke.side_effect = Exception("LLM error")
        
        response = self.agent.generate_response(result, "Update experiment EXP_001")
        
        assert "Successfully updated experiment" in response
    
    def test_generate_response_list_success_template(self):
        """Test template response for LIST success."""
        result = ExecutionResult(
            status="success",
            data=[{
                "status": "success",
                "action": {"action": "LIST", "entity": "PROJECT"},
                "result": "Found 2 projects"
            }]
        )
        
        # Mock LLM to raise exception to test fallback
        self.mock_llm.invoke.side_effect = Exception("LLM error")
        
        response = self.agent.generate_response(result, "List all projects")
        
        assert "Found results for projects" in response or "Retrieved projects successfully" in response
    
    def test_generate_response_no_data(self):
        """Test generating response when no data is present."""
        result = ExecutionResult(status="success", data=None)
        
        response = self.agent.generate_response(result, "Some query")
        
        assert "operation completed successfully" in response
    
    def test_generate_response_unexpected_status(self):
        """Test handling of unexpected status."""
        result = ExecutionResult(status="unknown_status")
        
        response = self.agent.generate_response(result, "Some query")
        
        assert "unexpected status" in response
    
    def test_generate_response_exception_handling(self):
        """Test handling of exceptions during response generation."""
        result = ExecutionResult(status="success", data="invalid_data_format")
        
        # This should not raise an exception
        response = self.agent.generate_response(result, "Some query")
        
        assert "encountered an error" in response
    
    def test_build_explainer_user_message(self):
        """Test building user message for LLM."""
        result = ExecutionResult(
            status="success",
            data=[{"test": "data"}],
            actions_executed=2,
            execution_time=1.5
        )
        
        user_context = {"username": "testuser", "space": "MY_SPACE"}
        
        message = self.agent._build_explainer_user_message(result, "Test query", user_context)
        
        assert "Original query: Test query" in message
        assert "Actions executed: 2" in message
        assert "Execution time: 1.50 seconds" in message
        assert "username: testuser" in message
        assert "space: MY_SPACE" in message
    
    def test_format_error_response_validation_error(self):
        """Test formatting validation error response."""
        result = ExecutionResult(
            status="error",
            error="Validation error: type field is required"
        )
        
        response = self.agent._format_error_response(result, "Create sample")
        
        assert "doesn't meet the requirements" in response
    
    def test_format_success_response_template_multiple_actions(self):
        """Test template formatting for multiple successful actions."""
        result = ExecutionResult(
            status="success",
            data=[
                {
                    "status": "success",
                    "action": {"action": "GET", "entity": "SAMPLE"},
                    "result": "Sample retrieved"
                },
                {
                    "status": "success", 
                    "action": {"action": "LIST", "entity": "EXPERIMENT"},
                    "result": "Found experiments"
                }
            ]
        )
        
        response = self.agent._format_success_response_template(result, "Get sample and list experiments")
        
        assert "Retrieved sample details successfully" in response
        assert "Found results for experiments" in response or "Retrieved experiments successfully" in response
