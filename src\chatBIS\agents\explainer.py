#!/usr/bin/env python3
"""
Response Generation Agent (Explainer) for chatBIS.

This agent translates structured execution results into clear, natural language
responses for the user. It handles both success and error scenarios with
appropriate messaging.
"""

import logging
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from langchain_core.messages import SystemMessage, HumanMessage

from ..models import ExecutionResult, ClarificationRequest, ActionType, EntityType

logger = logging.getLogger(__name__)


class ExplainerAgent:
    """Agent that generates natural language responses from structured execution results."""
    
    def __init__(self, llm):
        """
        Initialize the Explainer Agent.
        
        Args:
            llm: Language model instance for generating natural language responses
        """
        self.llm = llm
    
    def generate_response(self, result: Union[ExecutionResult, ClarificationRequest], 
                         original_query: str, user_context: Optional[Dict] = None) -> str:
        """
        Generate a natural language response from structured results.
        
        Args:
            result: ExecutionResult or ClarificationRequest to explain
            original_query: The original user query for context
            user_context: Additional context about the user/session
            
        Returns:
            Natural language response string
        """
        try:
            # Handle clarification requests directly
            if isinstance(result, ClarificationRequest):
                return self._format_clarification_response(result)
            
            # Handle different execution result statuses
            if result.status == "confirmation_needed":
                return result.confirmation_message or "Please confirm the action."
            
            elif result.status == "error":
                return self._format_error_response(result, original_query)
            
            elif result.status == "success":
                return self._format_success_response(result, original_query, user_context)
            
            else:
                return f"I received an unexpected status: {result.status}"
                
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I encountered an error while preparing my response. Please try again."
    
    def _format_clarification_response(self, clarification: ClarificationRequest) -> str:
        """Format a clarification request into natural language."""
        response = clarification.message
        
        if clarification.suggestions:
            response += f"\n\nSuggestions: {', '.join(clarification.suggestions)}"
        
        return response
    
    def _format_error_response(self, result: ExecutionResult, original_query: str) -> str:
        """Format an error result into a user-friendly message."""
        base_message = "I'm sorry, I encountered an error while processing your request."
        
        if result.error:
            # Clean up technical error messages for user consumption
            error_msg = result.error
            
            # Common error patterns and user-friendly translations
            if "not found" in error_msg.lower():
                base_message = "I couldn't find the requested item. Please check the identifier and try again."
            elif "permission" in error_msg.lower() or "access" in error_msg.lower():
                base_message = "You don't have permission to perform this action."
            elif "connection" in error_msg.lower():
                base_message = "I'm having trouble connecting to openBIS. Please check your connection."
            elif "validation" in error_msg.lower():
                base_message = "The provided information doesn't meet the requirements."
            else:
                base_message = f"Error: {error_msg}"
        
        return base_message
    
    def _format_success_response(self, result: ExecutionResult, original_query: str, 
                                user_context: Optional[Dict] = None) -> str:
        """Format a successful execution result into natural language."""
        if not result.data:
            return "The operation completed successfully."
        
        # Use LLM to generate natural language response
        try:
            system_prompt = self._build_explainer_system_prompt()
            user_message = self._build_explainer_user_message(result, original_query, user_context)
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_message)
            ]
            
            response = self.llm.invoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error using LLM for response generation: {e}")
            # Fallback to template-based response
            return self._format_success_response_template(result, original_query)
    
    def _build_explainer_system_prompt(self) -> str:
        """Build system prompt for the explainer LLM."""
        return """You are a helpful assistant that explains openBIS operation results to users in clear, natural language.

Your task is to take structured execution results and convert them into friendly, informative responses.

GUIDELINES:
1. Be conversational and helpful
2. Summarize key information without overwhelming technical details
3. For LIST operations, mention the count and key details
4. For GET operations, highlight the most important properties
5. For CREATE operations, confirm what was created and provide the identifier
6. For UPDATE operations, confirm what was changed
7. Use bullet points or numbered lists for multiple items
8. Keep responses concise but informative
9. Don't mention technical terms like "permId" unless necessary - use "identifier" instead

EXAMPLES:

For a LIST samples operation:
"I found 3 samples in the MY_SPACE space:
• SAMPLE_001 (Type: YEAST) - Created on 2024-01-15
• SAMPLE_002 (Type: BACTERIA) - Created on 2024-01-16  
• SAMPLE_003 (Type: YEAST) - Created on 2024-01-17"

For a GET sample operation:
"Here are the details for sample SAMPLE_001:
• Type: YEAST
• Space: MY_SPACE
• Description: Test sample for experiment
• Properties: NAME=Test Sample, NOTES=Initial batch"

For a CREATE operation:
"Success! I created a new sample with the following details:
• Code: NEW_SAMPLE_01
• Type: YEAST
• Space: MY_SPACE
• Identifier: /MY_SPACE/NEW_SAMPLE_01"

Remember to be helpful and clear in your explanations."""

    def _build_explainer_user_message(self, result: ExecutionResult, original_query: str, 
                                     user_context: Optional[Dict] = None) -> str:
        """Build the user message for the explainer LLM."""
        message_parts = [
            f"Original query: {original_query}",
            f"Execution results: {json.dumps(result.data, indent=2)}"
        ]
        
        if result.actions_executed:
            message_parts.append(f"Actions executed: {result.actions_executed}")
        
        if result.execution_time:
            message_parts.append(f"Execution time: {result.execution_time:.2f} seconds")
        
        if user_context:
            context_str = ", ".join([f"{k}: {v}" for k, v in user_context.items()])
            message_parts.append(f"User context: {context_str}")
        
        return "\n\n".join(message_parts)
    
    def _format_success_response_template(self, result: ExecutionResult, original_query: str) -> str:
        """Fallback template-based response formatting."""
        if not result.data or not isinstance(result.data, list):
            return "The operation completed successfully."
        
        responses = []
        
        for item in result.data:
            if item.get("status") == "success":
                action_info = item.get("action", {})
                action_type = action_info.get("action")
                entity_type = action_info.get("entity")
                tool_result = item.get("result", "")
                
                if action_type == "LIST":
                    # Try to count results
                    if "found" in str(tool_result).lower():
                        responses.append(f"Found results for {entity_type.lower()}s: {tool_result}")
                    else:
                        responses.append(f"Retrieved {entity_type.lower()}s successfully.")
                
                elif action_type == "GET":
                    responses.append(f"Retrieved {entity_type.lower()} details successfully.")
                
                elif action_type == "CREATE":
                    responses.append(f"Successfully created new {entity_type.lower()}.")
                
                elif action_type == "UPDATE":
                    responses.append(f"Successfully updated {entity_type.lower()}.")
        
        if responses:
            return " ".join(responses)
        else:
            return "The operation completed successfully."
