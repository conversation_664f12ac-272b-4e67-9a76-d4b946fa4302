#!/usr/bin/env python3
"""
Unit tests for IntentParserAgent.

Tests the natural language to ActionRequest JSON conversion functionality,
including handling of incomplete information and clarification requests.
"""

import json
import pytest
from unittest.mock import Mock, MagicMock
from langchain_core.messages import AIMessage

from src.chatBIS.agents.intent_parser import IntentParserAgent
from src.chatBIS.models import ActionRequest, ClarificationRequest, ActionType, EntityType


class TestIntentParserAgent:
    """Test cases for IntentParserAgent."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_llm = Mock()
        self.agent = IntentParserAgent(self.mock_llm)
    
    def test_parse_get_sample_query(self):
        """Test parsing a GET sample query."""
        # Mock LLM response for GET operation
        mock_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "GET",
                "entity": "SAMPLE", 
                "lookup_criteria": {"identifier": "SAMPLE_001"}
            }]
        }))
        self.mock_llm.invoke.return_value = mock_response
        
        result = self.agent.parse_intent("Show me sample SAMPLE_001")
        
        assert isinstance(result, ActionRequest)
        assert len(result.actions) == 1
        assert result.actions[0].action == ActionType.GET
        assert result.actions[0].entity == EntityType.SAMPLE
        assert result.actions[0].lookup_criteria.identifier == "SAMPLE_001"
    
    def test_parse_list_samples_query(self):
        """Test parsing a LIST samples query."""
        mock_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "LIST",
                "entity": "SAMPLE",
                "lookup_criteria": {"space": "MY_SPACE", "type": "YEAST"}
            }]
        }))
        self.mock_llm.invoke.return_value = mock_response
        
        result = self.agent.parse_intent("List all YEAST samples in MY_SPACE")
        
        assert isinstance(result, ActionRequest)
        assert len(result.actions) == 1
        assert result.actions[0].action == ActionType.LIST
        assert result.actions[0].entity == EntityType.SAMPLE
        assert result.actions[0].lookup_criteria.space == "MY_SPACE"
        assert result.actions[0].lookup_criteria.type == "YEAST"
    
    def test_parse_create_sample_query(self):
        """Test parsing a CREATE sample query."""
        mock_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "CREATE",
                "entity": "SAMPLE",
                "payload": {
                    "code": "NEW_SAMPLE_01",
                    "type": "YEAST",
                    "space": "MY_SPACE",
                    "properties": {"NOTES": "Test sample"}
                }
            }]
        }))
        self.mock_llm.invoke.return_value = mock_response
        
        result = self.agent.parse_intent("Create a new YEAST sample called NEW_SAMPLE_01 in MY_SPACE with notes 'Test sample'")
        
        assert isinstance(result, ActionRequest)
        assert len(result.actions) == 1
        assert result.actions[0].action == ActionType.CREATE
        assert result.actions[0].entity == EntityType.SAMPLE
        assert result.actions[0].payload.code == "NEW_SAMPLE_01"
        assert result.actions[0].payload.type == "YEAST"
        assert result.actions[0].payload.space == "MY_SPACE"
    
    def test_parse_clarification_needed(self):
        """Test handling when clarification is needed."""
        mock_response = AIMessage(content=json.dumps({
            "status": "clarification_needed",
            "message": "What type should the new sample have?",
            "missing_fields": ["type"]
        }))
        self.mock_llm.invoke.return_value = mock_response
        
        result = self.agent.parse_intent("Create a new sample called TEST01")
        
        assert isinstance(result, ClarificationRequest)
        assert result.status == "clarification_needed"
        assert "type" in result.message
        assert result.missing_fields == ["type"]
    
    def test_parse_invalid_json_response(self):
        """Test handling of invalid JSON from LLM."""
        mock_response = AIMessage(content="This is not valid JSON")
        self.mock_llm.invoke.return_value = mock_response
        
        result = self.agent.parse_intent("Show me samples")
        
        assert isinstance(result, ClarificationRequest)
        assert "trouble understanding" in result.message
    
    def test_parse_with_conversation_context(self):
        """Test parsing with conversation history context."""
        mock_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "LIST",
                "entity": "SAMPLE",
                "lookup_criteria": {"space": "MY_SPACE"}
            }]
        }))
        self.mock_llm.invoke.return_value = mock_response
        
        conversation_history = [
            {"content": "I'm working in MY_SPACE"},
            {"content": "That's great! How can I help?"}
        ]
        
        result = self.agent.parse_intent("Show me all samples", conversation_history=conversation_history)
        
        assert isinstance(result, ActionRequest)
        # Verify that conversation context was included in the LLM call
        call_args = self.mock_llm.invoke.call_args[0][0]
        user_message_content = call_args[1].content
        assert "Recent context" in user_message_content
    
    def test_parse_multiple_actions(self):
        """Test parsing query that results in multiple actions."""
        mock_response = AIMessage(content=json.dumps({
            "actions": [
                {
                    "action": "GET",
                    "entity": "SAMPLE",
                    "lookup_criteria": {"identifier": "SAMPLE_001"}
                },
                {
                    "action": "LIST",
                    "entity": "EXPERIMENT", 
                    "lookup_criteria": {"space": "MY_SPACE"}
                }
            ]
        }))
        self.mock_llm.invoke.return_value = mock_response
        
        result = self.agent.parse_intent("Show me SAMPLE_001 and list all experiments in MY_SPACE")
        
        assert isinstance(result, ActionRequest)
        assert len(result.actions) == 2
        assert result.actions[0].action == ActionType.GET
        assert result.actions[1].action == ActionType.LIST
    
    def test_parse_update_query(self):
        """Test parsing an UPDATE query."""
        mock_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "UPDATE",
                "entity": "SAMPLE",
                "lookup_criteria": {"identifier": "SAMPLE_001"},
                "payload": {
                    "properties": {"NOTES": "Updated notes"}
                }
            }]
        }))
        self.mock_llm.invoke.return_value = mock_response
        
        result = self.agent.parse_intent("Update SAMPLE_001 notes to 'Updated notes'")
        
        assert isinstance(result, ActionRequest)
        assert len(result.actions) == 1
        assert result.actions[0].action == ActionType.UPDATE
        assert result.actions[0].lookup_criteria.identifier == "SAMPLE_001"
        assert result.actions[0].payload.properties["NOTES"] == "Updated notes"
    
    def test_llm_exception_handling(self):
        """Test handling of LLM exceptions."""
        self.mock_llm.invoke.side_effect = Exception("LLM connection error")
        
        result = self.agent.parse_intent("Show me samples")
        
        assert isinstance(result, ClarificationRequest)
        assert "encountered an error" in result.message
    
    def test_validation_error_handling(self):
        """Test handling of Pydantic validation errors."""
        # Mock response with invalid action type
        mock_response = AIMessage(content=json.dumps({
            "actions": [{
                "action": "INVALID_ACTION",
                "entity": "SAMPLE"
            }]
        }))
        self.mock_llm.invoke.return_value = mock_response
        
        result = self.agent.parse_intent("Do something with samples")
        
        assert isinstance(result, ClarificationRequest)
        assert "more information" in result.message
